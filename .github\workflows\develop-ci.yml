# Develop分支持续集成
# 当代码推送到develop分支时触发
name: Develop分支CI

on:
  push:
    branches: [ develop ]
  workflow_dispatch:

jobs:
  # 完整测试套件
  full-test-suite:
    name: 完整测试套件
    runs-on: ubuntu-22.04  # 明确指定Ubuntu 22.04以避免Ubuntu 24.04兼容性问题

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 设置Python环境
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'

    - name: 缓存依赖
      uses: actions/cache@v4  # 升级到v4版本
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
          
    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest-cov pytest-xvfb flake8 black isort
        
    - name: 运行完整测试
      run: |
        echo "🧪 运行完整测试套件..."
        python scripts/run_tests.py
        pytest tests/ -v --cov=src --cov-report=xml --cov-report=html
        
    - name: 上传测试覆盖率
      uses: codecov/codecov-action@v4
      with:
        file: ./coverage.xml
        flags: develop
        name: develop-coverage

    - name: 保存覆盖率报告
      uses: actions/upload-artifact@v4
      with:
        name: coverage-report
        path: htmlcov/

  # 简化多平台构建测试 - 只测试核心组合
  multi-platform-build:
    name: 多平台构建测试
    strategy:
      matrix:
        os: [ubuntu-22.04, windows-latest]  # 明确指定Ubuntu 22.04
        python-version: ['3.11']  # 只测试主要Python版本

    runs-on: ${{ matrix.os }}
    continue-on-error: true  # 允许失败

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 设置Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}

    - name: 安装系统依赖 (Linux)
      if: matrix.os == 'ubuntu-22.04'
      run: |
        sudo apt-get update
        sudo apt-get install -y \
          libgl1-mesa-glx \
          libglib2.0-0 \
          libxkbcommon-x11-0 \
          libxcb-icccm4 \
          libxcb-image0 \
          libxcb-keysyms1 \
          libxcb-randr0 \
          libxcb-render-util0 \
          libxcb-xinerama0 \
          libxcb-xfixes0

    - name: 安装Python依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: 运行基础测试
      run: |
        echo "Running tests on ${{ matrix.os }} Python ${{ matrix.python-version }}..."
        python scripts/run_tests.py

  # 性能测试
  performance-test:
    name: 性能测试
    runs-on: ubuntu-22.04  # 明确指定Ubuntu 22.04

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 设置Python环境
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'
        
    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest-benchmark memory-profiler
        
    - name: 运行性能测试
      run: |
        echo "⚡ 运行性能测试..."
        # 这里可以添加性能测试脚本
        python -c "
        import time
        import psutil
        import os
        
        print('📊 系统性能基准测试')
        print(f'CPU核心数: {psutil.cpu_count()}')
        print(f'内存总量: {psutil.virtual_memory().total / 1024**3:.2f} GB')
        
        # 测试导入时间
        start_time = time.time()
        try:
            import sys
            sys.path.insert(0, 'src')
            from models.email_model import EmailModel
            from models.config_model import ConfigModel
            from services.database_service import DatabaseService
            import_time = time.time() - start_time
            print(f'✅ 模块导入时间: {import_time:.3f}s')
        except Exception as e:
            print(f'❌ 模块导入失败: {e}')
        "

  # 简化代码质量分析
  code-analysis:
    name: 代码质量分析
    runs-on: ubuntu-22.04  # 明确指定Ubuntu 22.04
    continue-on-error: true  # 允许失败

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 设置Python环境
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'

    - name: 基础代码检查
      run: |
        echo "📈 基础代码质量检查..."
        python -c "
        import os
        from pathlib import Path

        src_dir = Path('src')
        if src_dir.exists():
            py_files = list(src_dir.rglob('*.py'))
            print(f'✅ 找到 {len(py_files)} 个Python文件')

            total_lines = 0
            for py_file in py_files:
                try:
                    with open(py_file, 'r', encoding='utf-8') as f:
                        lines = len(f.readlines())
                        total_lines += lines
                except:
                    continue

            print(f'✅ 总代码行数: {total_lines}')
            print('✅ 代码质量检查完成')
        else:
            print('⚠️ src目录不存在')
        "

  # 简化安全扫描 - 只运行本地脚本
  security-scan:
    name: 安全扫描
    runs-on: ubuntu-22.04  # 明确指定Ubuntu 22.04
    continue-on-error: true  # 允许失败

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 设置Python环境
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'

    - name: 运行本地安全检查
      run: |
        echo "🔒 运行本地安全检查..."
        python scripts/security_check.py

  # 文档检查
  documentation-check:
    name: 文档检查
    runs-on: ubuntu-22.04  # 明确指定Ubuntu 22.04

    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 检查文档完整性
      run: |
        echo "📚 检查文档完整性..."
        
        # 检查必要文档是否存在
        required_docs=("README.md" "GEMINI.md" "docs/architecture-design.md" "docs/development-plan.md")
        
        for doc in "${required_docs[@]}"; do
          if [[ -f "$doc" ]]; then
            echo "✅ 文档存在: $doc"
          else
            echo "❌ 缺少文档: $doc"
            exit 1
          fi
        done
        
        # 检查README文档的基本结构
        if grep -q "## 📧 项目简介" README.md && \
           grep -q "## ✨ 主要功能" README.md && \
           grep -q "## 🏗️ 技术架构" README.md; then
          echo "✅ README文档结构完整"
        else
          echo "❌ README文档结构不完整"
          exit 1
        fi

  # 通知 - 宽松模式
  notification:
    name: 构建通知
    runs-on: ubuntu-22.04  # 明确指定Ubuntu 22.04
    needs: [full-test-suite, multi-platform-build, performance-test, code-analysis, security-scan, documentation-check]
    if: always()

    steps:
    - name: 构建结果通知
      run: |
        echo "📢 Develop分支CI构建完成"
        echo "=========================="

        # 只检查核心测试是否通过
        if [[ "${{ needs.full-test-suite.result }}" == "success" ]]; then
          echo "✅ 核心测试通过"
          echo "🎉 Develop分支状态良好！"
        else
          echo "❌ 核心测试失败，需要修复"
        fi

        # 显示其他检查状态（但不影响整体结果）
        echo ""
        echo "其他检查状态:"
        [[ "${{ needs.multi-platform-build.result }}" == "success" ]] && echo "  ✅ 多平台构建测试" || echo "  ⚠️ 多平台构建测试"
        [[ "${{ needs.performance-test.result }}" == "success" ]] && echo "  ✅ 性能测试" || echo "  ⚠️ 性能测试"
        [[ "${{ needs.code-analysis.result }}" == "success" ]] && echo "  ✅ 代码质量分析" || echo "  ⚠️ 代码质量分析"
        [[ "${{ needs.security-scan.result }}" == "success" ]] && echo "  ✅ 安全扫描" || echo "  ⚠️ 安全扫描"
        [[ "${{ needs.documentation-check.result }}" == "success" ]] && echo "  ✅ 文档检查" || echo "  ⚠️ 文档检查"
